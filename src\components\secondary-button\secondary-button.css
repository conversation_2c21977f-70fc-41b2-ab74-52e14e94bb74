.secondary-button {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 32px;
  background: var(--color-support);
  color: var(--color-content-accent);
  border: none;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  padding: 8px 16px;
  cursor: pointer;
  transition: background 0.2s ease, color 0.2s ease;
}

.secondary-button:hover:not(:disabled) {
  background: var(--color-bg-hover);
}

.secondary-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.secondary-button__icon {
  width: 16px;
  height: 16px;
}
.secondary-button--small {
  padding: 6px 12px;
  font-size: var(--font-size-base);
}
.secondary-button--large {
  padding: 10px 20px;
  font-size: var(--font-size-base);
}
.secondary-button--full-width {
  width: 100%;
  justify-content: center;
}