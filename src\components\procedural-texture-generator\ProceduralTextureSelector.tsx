import React, { useState, useCallback } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment } from '@react-three/drei';
import { ProceduralMaterial, ProceduralTextureType, DEFAULT_PARAMS } from './ProceduralMaterial';
import './ProceduralTextureSelector.css';

interface ProceduralTextureSelectorProps {
  onTextureSelect?: (material: ProceduralMaterial) => void;
  selectedType?: ProceduralTextureType;
  className?: string;
}

// 预设纹理选项
const PRESET_TEXTURES = [
  {
    type: ProceduralTextureType.CARRARA_MARBLE,
    name: '卡拉拉大理石',
    description: '经典白色大理石'
  },
  {
    type: ProceduralTextureType.BLACK_MARBLE,
    name: '黑色大理石',
    description: '优雅黑色大理石'
  },
  {
    type: ProceduralTextureType.GREEN_MARBLE,
    name: '绿色大理石',
    description: '自然绿色大理石'
  },
  {
    type: ProceduralTextureType.OAK,
    name: '橡木',
    description: '经典橡木纹理'
  },
  {
    type: ProceduralTextureType.WALNUT,
    name: '胡桃木',
    description: '深色胡桃木纹理'
  },
  {
    type: ProceduralTextureType.PINE,
    name: '松木',
    description: '浅色松木纹理'
  },
  {
    type: ProceduralTextureType.CHERRY,
    name: '樱桃木',
    description: '温暖樱桃木纹理'
  },
  {
    type: ProceduralTextureType.BAMBOO,
    name: '竹子',
    description: '天然竹子纹理'
  }
];

// 纹理预览卡片组件
const TexturePreviewCard: React.FC<{
  texture: typeof PRESET_TEXTURES[0];
  isSelected: boolean;
  onClick: () => void;
}> = ({ texture, isSelected, onClick }) => {
  const [material, setMaterial] = useState<ProceduralMaterial | null>(null);

  React.useEffect(() => {
    const newMaterial = new ProceduralMaterial({ 
      type: texture.type, 
      ...DEFAULT_PARAMS[texture.type] 
    });
    setMaterial(newMaterial);

    return () => {
      newMaterial.dispose();
    };
  }, [texture.type]);

  return (
    <div 
      className={`texture-preview-card ${isSelected ? 'selected' : ''}`}
      onClick={onClick}
    >
      <div className="texture-preview-canvas">
        <Canvas
          frameloop="demand"
          camera={{ position: [0, 0, 2], fov: 45 }}
          gl={{ antialias: true, alpha: true }}
        >
          <ambientLight intensity={0.7} />
          <directionalLight position={[3, 3, 3]} intensity={1} />
          {material && (
            <mesh>
              <planeGeometry args={[1.5, 1.5]} />
              <primitive object={material} attach="material" />
            </mesh>
          )}
          <Environment preset="city" />
        </Canvas>
      </div>
      <div className="texture-info">
        <h4 className="texture-name">{texture.name}</h4>
        <p className="texture-description">{texture.description}</p>
      </div>
    </div>
  );
};

export const ProceduralTextureSelector: React.FC<ProceduralTextureSelectorProps> = ({
  onTextureSelect,
  selectedType,
  className = ''
}) => {
  const [currentSelection, setCurrentSelection] = useState<ProceduralTextureType | null>(
    selectedType || null
  );

  const handleTextureSelect = useCallback((textureType: ProceduralTextureType) => {
    setCurrentSelection(textureType);
    
    // 创建选中的材质
    const material = new ProceduralMaterial({ 
      type: textureType, 
      ...DEFAULT_PARAMS[textureType] 
    });
    
    onTextureSelect?.(material);
  }, [onTextureSelect]);

  return (
    <div className={`procedural-texture-selector ${className}`}>
      <div className="selector-header">
        <h3 className="selector-title">程序化纹理</h3>
        <p className="selector-subtitle">选择一种程序化生成的纹理</p>
      </div>
      
      <div className="texture-grid">
        {PRESET_TEXTURES.map((texture) => (
          <TexturePreviewCard
            key={texture.type}
            texture={texture}
            isSelected={currentSelection === texture.type}
            onClick={() => handleTextureSelect(texture.type)}
          />
        ))}
      </div>
    </div>
  );
};

export default ProceduralTextureSelector;
