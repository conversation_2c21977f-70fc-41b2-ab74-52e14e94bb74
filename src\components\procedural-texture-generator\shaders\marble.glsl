// 大理石纹理 Shader

// 引入噪声函数
// 注意：在实际使用中，这些函数会被包含在主 shader 中

// 大理石纹理生成函数
vec3 generateMarbleTexture(vec2 uv, float scale, float turbulence, vec3 color1, vec3 color2, vec3 veinColor) {
    // 缩放 UV 坐标
    vec2 scaledUV = uv * scale;
    
    // 基础波形 - 创建大理石的主要条纹
    float basePattern = sin(scaledUV.x * 3.14159 + scaledUV.y * 0.5);
    
    // 添加噪声扰动来创建自然的大理石纹理
    float noiseValue = fbm(scaledUV * 2.0) * turbulence;
    float distortedPattern = basePattern + noiseValue;
    
    // 创建更复杂的纹理层
    float layer1 = sin(distortedPattern * 4.0);
    float layer2 = sin(distortedPattern * 8.0 + noiseValue * 2.0);
    float layer3 = sin(distortedPattern * 16.0 + noiseValue * 4.0);
    
    // 组合不同的层
    float combinedPattern = (layer1 + layer2 * 0.5 + layer3 * 0.25) / 1.75;
    
    // 创建大理石的静脉
    float veinPattern = abs(sin(distortedPattern * 12.0 + noiseValue * 6.0));
    veinPattern = smoothstep(0.8, 1.0, veinPattern);
    
    // 基础颜色混合
    vec3 baseColor = mix(color1, color2, (combinedPattern + 1.0) * 0.5);
    
    // 添加静脉颜色
    vec3 finalColor = mix(baseColor, veinColor, veinPattern * 0.7);
    
    return finalColor;
}

// 高级大理石纹理（带更多细节）
vec3 generateAdvancedMarbleTexture(vec2 uv, float scale, float turbulence, vec3 color1, vec3 color2, vec3 veinColor, float veinIntensity) {
    vec2 scaledUV = uv * scale;
    
    // 多层噪声
    float noise1 = fbm(scaledUV * 1.0) * 0.5;
    float noise2 = fbm(scaledUV * 2.0) * 0.3;
    float noise3 = fbm(scaledUV * 4.0) * 0.2;
    float combinedNoise = (noise1 + noise2 + noise3) * turbulence;
    
    // 主要的大理石条纹
    float mainPattern = sin((scaledUV.x + scaledUV.y * 0.3) * 6.28318 + combinedNoise * 3.0);
    
    // 次要条纹
    float secondaryPattern = sin((scaledUV.x * 1.5 - scaledUV.y * 0.7) * 4.0 + combinedNoise * 2.0);
    
    // 细节条纹
    float detailPattern = sin((scaledUV.x * 3.0 + scaledUV.y * 2.0) * 8.0 + combinedNoise * 4.0);
    
    // 组合所有条纹
    float finalPattern = (mainPattern + secondaryPattern * 0.6 + detailPattern * 0.3) / 1.9;
    
    // 创建静脉
    float vein1 = abs(sin((scaledUV.x + combinedNoise) * 20.0));
    float vein2 = abs(sin((scaledUV.y + combinedNoise * 0.7) * 15.0));
    float veinPattern = min(vein1, vein2);
    veinPattern = smoothstep(0.85, 1.0, veinPattern) * veinIntensity;
    
    // 颜色混合
    vec3 baseColor = mix(color1, color2, (finalPattern + 1.0) * 0.5);
    vec3 finalColor = mix(baseColor, veinColor, veinPattern);
    
    return finalColor;
}

// 卡拉拉大理石风格
vec3 generateCarraraMarble(vec2 uv, float scale) {
    vec3 white = vec3(0.95, 0.95, 0.95);
    vec3 lightGray = vec3(0.85, 0.85, 0.87);
    vec3 darkGray = vec3(0.6, 0.6, 0.65);
    
    return generateAdvancedMarbleTexture(uv, scale, 0.8, white, lightGray, darkGray, 0.3);
}

// 黑色大理石风格
vec3 generateBlackMarble(vec2 uv, float scale) {
    vec3 black = vec3(0.1, 0.1, 0.1);
    vec3 darkGray = vec3(0.2, 0.2, 0.25);
    vec3 gold = vec3(0.8, 0.7, 0.4);
    
    return generateAdvancedMarbleTexture(uv, scale, 1.2, black, darkGray, gold, 0.2);
}

// 绿色大理石风格
vec3 generateGreenMarble(vec2 uv, float scale) {
    vec3 darkGreen = vec3(0.1, 0.3, 0.2);
    vec3 lightGreen = vec3(0.3, 0.5, 0.4);
    vec3 white = vec3(0.9, 0.95, 0.9);
    
    return generateAdvancedMarbleTexture(uv, scale, 1.0, darkGreen, lightGreen, white, 0.4);
}
