// 木纹纹理 Shader

// 木纹纹理生成函数
vec3 generateWoodTexture(vec2 uv, float scale, float ringSpacing, vec3 lightWood, vec3 darkWood, float grainIntensity) {
    // 缩放 UV 坐标
    vec2 scaledUV = uv * scale;
    
    // 计算到中心的距离（用于年轮）
    vec2 center = vec2(0.5, 0.5);
    float distanceFromCenter = length(scaledUV - center);
    
    // 添加噪声来扰动年轮
    float noise = fbm(scaledUV * 8.0) * 0.1;
    float distortedDistance = distanceFromCenter + noise;
    
    // 创建年轮图案
    float rings = sin(distortedDistance * ringSpacing * 3.14159 * 2.0);
    
    // 添加木纹细节
    float grain1 = fbm(vec2(scaledUV.x * 20.0, scaledUV.y * 2.0)) * grainIntensity;
    float grain2 = fbm(vec2(scaledUV.x * 40.0, scaledUV.y * 4.0)) * grainIntensity * 0.5;
    float totalGrain = grain1 + grain2;
    
    // 组合年轮和木纹
    float woodPattern = (rings + totalGrain) * 0.5 + 0.5;
    
    // 混合颜色
    vec3 finalColor = mix(lightWood, darkWood, woodPattern);
    
    return finalColor;
}

// 高级木纹纹理（带节疤和更复杂的纹理）
vec3 generateAdvancedWoodTexture(vec2 uv, float scale, float ringSpacing, vec3 lightWood, vec3 darkWood, float grainIntensity, float knotIntensity) {
    vec2 scaledUV = uv * scale;
    
    // 基础年轮
    vec2 center = vec2(0.3, 0.7); // 偏移中心点使其更自然
    float distanceFromCenter = length(scaledUV - center);
    
    // 多层噪声扰动
    float noise1 = fbm(scaledUV * 6.0) * 0.15;
    float noise2 = fbm(scaledUV * 12.0) * 0.08;
    float distortedDistance = distanceFromCenter + noise1 + noise2;
    
    // 年轮图案
    float rings = sin(distortedDistance * ringSpacing * 6.28318);
    rings = (rings + 1.0) * 0.5; // 归一化到 0-1
    
    // 木纹方向
    float grainDirection = scaledUV.y + fbm(vec2(scaledUV.x * 10.0, scaledUV.y * 2.0)) * 0.2;
    float grain = sin(grainDirection * 50.0) * grainIntensity;
    
    // 添加节疤
    vec2 knotCenter1 = vec2(0.2, 0.3);
    vec2 knotCenter2 = vec2(0.8, 0.7);
    float knot1 = 1.0 - smoothstep(0.0, 0.1, length(scaledUV - knotCenter1));
    float knot2 = 1.0 - smoothstep(0.0, 0.08, length(scaledUV - knotCenter2));
    float knots = (knot1 + knot2) * knotIntensity;
    
    // 组合所有元素
    float woodPattern = rings * 0.6 + grain * 0.3 + knots * 0.1;
    woodPattern = clamp(woodPattern, 0.0, 1.0);
    
    // 颜色混合
    vec3 finalColor = mix(lightWood, darkWood, woodPattern);
    
    // 在节疤区域添加更深的颜色
    vec3 knotColor = darkWood * 0.5;
    finalColor = mix(finalColor, knotColor, knots * 0.7);
    
    return finalColor;
}

// 橡木纹理
vec3 generateOakTexture(vec2 uv, float scale) {
    vec3 lightOak = vec3(0.8, 0.65, 0.4);
    vec3 darkOak = vec3(0.5, 0.35, 0.2);
    
    return generateAdvancedWoodTexture(uv, scale, 15.0, lightOak, darkOak, 0.3, 0.4);
}

// 松木纹理
vec3 generatePineTexture(vec2 uv, float scale) {
    vec3 lightPine = vec3(0.9, 0.8, 0.6);
    vec3 darkPine = vec3(0.7, 0.5, 0.3);
    
    return generateAdvancedWoodTexture(uv, scale, 20.0, lightPine, darkPine, 0.2, 0.2);
}

// 胡桃木纹理
vec3 generateWalnutTexture(vec2 uv, float scale) {
    vec3 lightWalnut = vec3(0.6, 0.4, 0.25);
    vec3 darkWalnut = vec3(0.3, 0.2, 0.15);
    
    return generateAdvancedWoodTexture(uv, scale, 12.0, lightWalnut, darkWalnut, 0.4, 0.3);
}

// 樱桃木纹理
vec3 generateCherryTexture(vec2 uv, float scale) {
    vec3 lightCherry = vec3(0.8, 0.5, 0.3);
    vec3 darkCherry = vec3(0.5, 0.25, 0.15);
    
    return generateAdvancedWoodTexture(uv, scale, 18.0, lightCherry, darkCherry, 0.25, 0.35);
}

// 竹子纹理
vec3 generateBambooTexture(vec2 uv, float scale) {
    vec2 scaledUV = uv * scale;
    
    // 竹节图案
    float segments = sin(scaledUV.y * 10.0) * 0.1 + 0.9;
    
    // 竹子的纵向纹理
    float verticalGrain = fbm(vec2(scaledUV.x * 30.0, scaledUV.y * 5.0)) * 0.2;
    
    // 组合图案
    float bambooPattern = segments + verticalGrain;
    
    vec3 lightBamboo = vec3(0.85, 0.8, 0.6);
    vec3 darkBamboo = vec3(0.6, 0.55, 0.4);
    
    return mix(lightBamboo, darkBamboo, bambooPattern);
}
