/* 模态框基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--loading-backdrop);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: modal-fade-in 0.2s ease-out;
}

.modal-container {
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-m);
  box-shadow: var(--shadow-modal);
  max-height: 90vh;
  overflow-y: auto;
  animation: modal-slide-in 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
}

.modal-close-button {
  color: var(--color-content-mute);
}

.modal-close-button:hover {
  color: var(--color-content-accent);
}

.modal-content {
  padding: 24px;
}

/* 尺寸变体 */
.modal-container--small {
  width: 400px;
  max-width: 90%;
}

.modal-container--medium {
  width: 500px;
  max-width: 90%;
}

.modal-container--large {
  width: 700px;
  max-width: 90%;
}

.modal-container--full {
  width: 90%;
  height: 90%;
  max-width: none;
  max-height: none;
}

.modal-container--full .modal-content {
  height: calc(100% - 80px);
  overflow-y: auto;
}

/* 动画效果 */
@keyframes modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modal-slide-in {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 表单样式（在模态框中使用） */
.modal-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.modal-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modal-form .form-group label {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-regular);
}

.modal-form .form-input {
  width: 100%;
  padding: 10px 12px;
  font-size: var(--font-size-base);
  color: var(--color-content-accent);
  background-color: var(--color-bg-input);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-s);
  outline: none;
  transition: border-color 0.2s;
}

.modal-form .form-input:focus {
  border-color: var(--color-brand);
}

.modal-form .modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border);
}
