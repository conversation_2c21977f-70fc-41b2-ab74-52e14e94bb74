import React, { useState, useEffect, useCallback } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment } from '@react-three/drei';
import * as THREE from 'three';
import { ProceduralMaterial, ProceduralTextureType, ProceduralTextureParams, DEFAULT_PARAMS } from './ProceduralMaterial';
import { Slider } from '../slider/slider';
import { DropDown } from '../drop-down/drop-down';
import './ProceduralTextureGenerator.css';

interface ProceduralTextureGeneratorProps {
  onTextureChange?: (material: ProceduralMaterial) => void;
  initialType?: ProceduralTextureType;
  className?: string;
}

// 纹理类型选项
const TEXTURE_TYPE_OPTIONS = [
  { value: ProceduralTextureType.NOISE, label: '噪声纹理' },
  { value: ProceduralTextureType.MARBLE, label: '自定义大理石' },
  { value: ProceduralTextureType.CARRARA_MARBLE, label: '卡拉拉大理石' },
  { value: ProceduralTextureType.BLACK_MARBLE, label: '黑色大理石' },
  { value: ProceduralTextureType.GREEN_MARBLE, label: '绿色大理石' },
  { value: ProceduralTextureType.WOOD, label: '自定义木纹' },
  { value: ProceduralTextureType.OAK, label: '橡木' },
  { value: ProceduralTextureType.PINE, label: '松木' },
  { value: ProceduralTextureType.WALNUT, label: '胡桃木' },
  { value: ProceduralTextureType.CHERRY, label: '樱桃木' },
  { value: ProceduralTextureType.BAMBOO, label: '竹子' }
];

// 预览球体组件
const PreviewSphere: React.FC<{ material: ProceduralMaterial }> = ({ material }) => {
  return (
    <mesh>
      <sphereGeometry args={[1, 64, 64]} />
      <primitive object={material} attach="material" />
    </mesh>
  );
};

export const ProceduralTextureGenerator: React.FC<ProceduralTextureGeneratorProps> = ({
  onTextureChange,
  initialType = ProceduralTextureType.MARBLE,
  className = ''
}) => {
  const [currentType, setCurrentType] = useState<ProceduralTextureType>(initialType);
  const [params, setParams] = useState<ProceduralTextureParams>(DEFAULT_PARAMS[initialType]);
  const [material, setMaterial] = useState<ProceduralMaterial | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 创建材质
  const createMaterial = useCallback(async (type: ProceduralTextureType, newParams?: Partial<ProceduralTextureParams>) => {
    setIsLoading(true);
    setError(null);

    try {
      // 检查 Shader 支持
      if (!ProceduralMaterial.isShaderSupported()) {
        throw new Error('当前设备不支持程序化纹理所需的 WebGL 功能');
      }

      const finalParams = newParams ? { ...params, ...newParams } : params;
      const newMaterial = new ProceduralMaterial({ type, ...finalParams });

      // 获取性能信息并在控制台输出
      const perfInfo = newMaterial.getPerformanceInfo();
      console.log('程序化纹理性能信息:', perfInfo);

      setMaterial(newMaterial);
      onTextureChange?.(newMaterial);
      return newMaterial;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建程序化纹理失败';
      setError(errorMessage);
      console.error('创建程序化纹理失败:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [params, onTextureChange]);

  // 初始化材质
  useEffect(() => {
    createMaterial(currentType);
    return () => {
      if (material) {
        material.dispose();
      }
    };
  }, []);

  // 处理纹理类型变化
  const handleTypeChange = (newType: ProceduralTextureType) => {
    setCurrentType(newType);
    const newParams = DEFAULT_PARAMS[newType];
    setParams(newParams);
    
    if (material) {
      material.dispose();
    }
    createMaterial(newType, newParams);
  };

  // 处理参数变化
  const handleParamChange = (key: keyof ProceduralTextureParams, value: any) => {
    const newParams = { ...params, [key]: value };
    setParams(newParams);
    
    if (material) {
      material.updateParams({ [key]: value });
    }
  };

  // 处理颜色变化
  const handleColorChange = (colorKey: 'color1' | 'color2' | 'veinColor', hexColor: string) => {
    const color = new THREE.Color(hexColor);
    handleParamChange(colorKey, color);
  };

  // 获取当前纹理类型的可用参数
  const getAvailableParams = () => {
    const isMarble = currentType.includes('marble') || currentType === ProceduralTextureType.MARBLE;
    const isWood = currentType.includes('wood') || 
                   [ProceduralTextureType.OAK, ProceduralTextureType.PINE, 
                    ProceduralTextureType.WALNUT, ProceduralTextureType.CHERRY, 
                    ProceduralTextureType.BAMBOO].includes(currentType);
    const isCustom = currentType === ProceduralTextureType.MARBLE || currentType === ProceduralTextureType.WOOD;

    return {
      scale: true,
      turbulence: isMarble || currentType === ProceduralTextureType.NOISE,
      ringSpacing: isWood && currentType !== ProceduralTextureType.BAMBOO,
      grainIntensity: isWood,
      knotIntensity: isWood && currentType !== ProceduralTextureType.BAMBOO,
      veinIntensity: isMarble,
      colors: isCustom
    };
  };

  const availableParams = getAvailableParams();

  return (
    <div className={`procedural-texture-generator ${className} ${isLoading ? 'loading' : ''} ${error ? 'error' : ''}`}>
      <div className="texture-preview">
        <div className="preview-canvas">
          {error ? (
            <div className="error-message">
              <p>纹理生成失败</p>
              <small>{error}</small>
            </div>
          ) : (
            <Canvas
              camera={{ position: [0, 0, 3], fov: 45 }}
              gl={{ antialias: true, alpha: true }}
            >
              <ambientLight intensity={0.7} />
              <directionalLight position={[5, 5, 5]} intensity={1} />
              {material && !isLoading && <PreviewSphere material={material} />}
              <Environment preset="city" />
            </Canvas>
          )}
        </div>
      </div>

      <div className="texture-controls">
        <div className="control-group">
          <label className="control-label">纹理类型</label>
          <DropDown
            options={TEXTURE_TYPE_OPTIONS}
            value={currentType}
            onChange={handleTypeChange}
            placeholder="选择纹理类型"
          />
        </div>

        <div className="control-group">
          <label className="control-label">缩放</label>
          <Slider
            min={0.1}
            max={10}
            step={0.1}
            defaultValue={params.scale}
            onChange={(value) => handleParamChange('scale', value)}
            showValue={true}
            width="100%"
          />
        </div>

        {availableParams.turbulence && (
          <div className="control-group">
            <label className="control-label">湍流强度</label>
            <Slider
              min={0}
              max={2}
              step={0.1}
              defaultValue={params.turbulence || 1}
              onChange={(value) => handleParamChange('turbulence', value)}
              showValue={true}
              width="100%"
            />
          </div>
        )}

        {availableParams.ringSpacing && (
          <div className="control-group">
            <label className="control-label">年轮间距</label>
            <Slider
              min={5}
              max={30}
              step={1}
              defaultValue={params.ringSpacing || 15}
              onChange={(value) => handleParamChange('ringSpacing', value)}
              showValue={true}
              width="100%"
            />
          </div>
        )}

        {availableParams.grainIntensity && (
          <div className="control-group">
            <label className="control-label">木纹强度</label>
            <Slider
              min={0}
              max={1}
              step={0.05}
              defaultValue={params.grainIntensity || 0.3}
              onChange={(value) => handleParamChange('grainIntensity', value)}
              showValue={true}
              width="100%"
            />
          </div>
        )}

        {availableParams.knotIntensity && (
          <div className="control-group">
            <label className="control-label">节疤强度</label>
            <Slider
              min={0}
              max={1}
              step={0.05}
              defaultValue={params.knotIntensity || 0.2}
              onChange={(value) => handleParamChange('knotIntensity', value)}
              showValue={true}
              width="100%"
            />
          </div>
        )}

        {availableParams.veinIntensity && (
          <div className="control-group">
            <label className="control-label">静脉强度</label>
            <Slider
              min={0}
              max={1}
              step={0.05}
              defaultValue={params.veinIntensity || 0.5}
              onChange={(value) => handleParamChange('veinIntensity', value)}
              showValue={true}
              width="100%"
            />
          </div>
        )}

        {availableParams.colors && (
          <div className="color-controls">
            <div className="control-group">
              <label className="control-label">主色调</label>
              <input
                type="color"
                value={`#${params.color1?.getHexString() || 'ffffff'}`}
                onChange={(e) => handleColorChange('color1', e.target.value)}
                className="color-input"
              />
            </div>

            <div className="control-group">
              <label className="control-label">次色调</label>
              <input
                type="color"
                value={`#${params.color2?.getHexString() || '888888'}`}
                onChange={(e) => handleColorChange('color2', e.target.value)}
                className="color-input"
              />
            </div>

            {availableParams.veinIntensity && (
              <div className="control-group">
                <label className="control-label">静脉颜色</label>
                <input
                  type="color"
                  value={`#${params.veinColor?.getHexString() || '444444'}`}
                  onChange={(e) => handleColorChange('veinColor', e.target.value)}
                  className="color-input"
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProceduralTextureGenerator;
