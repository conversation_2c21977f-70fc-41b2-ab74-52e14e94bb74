.procedural-texture-generator {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--color-bg-secondary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-subtle);
}

.texture-preview {
  width: 100%;
  height: 200px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background: var(--color-bg-input);
  border: 1px solid var(--color-border-subtle);
}

.preview-canvas {
  width: 100%;
  height: 100%;
}

.texture-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.control-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-primary);
  margin-bottom: var(--spacing-xs);
}

.color-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--color-bg-tertiary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-border-subtle);
}

.color-input {
  width: 100%;
  height: 40px;
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  background: transparent;
  padding: 0;
}

.color-input::-webkit-color-swatch-wrapper {
  padding: 0;
  border: none;
  border-radius: var(--border-radius-sm);
}

.color-input::-webkit-color-swatch {
  border: none;
  border-radius: var(--border-radius-sm);
}

.color-input::-moz-color-swatch {
  border: none;
  border-radius: var(--border-radius-sm);
}

.color-input:hover {
  border-color: var(--color-border-primary);
}

.color-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .procedural-texture-generator {
    padding: var(--spacing-sm);
  }
  
  .texture-preview {
    height: 150px;
  }
  
  .color-controls {
    grid-template-columns: 1fr;
  }
}

/* 深色主题适配 */
[data-theme="dark"] .procedural-texture-generator {
  background: var(--color-bg-secondary);
  border-color: var(--color-border-subtle);
}

[data-theme="dark"] .texture-preview {
  background: var(--color-bg-input);
  border-color: var(--color-border-subtle);
}

[data-theme="dark"] .color-controls {
  background: var(--color-bg-tertiary);
  border-color: var(--color-border-subtle);
}

/* 动画效果 */
.procedural-texture-generator {
  transition: all 0.2s ease;
}

.texture-preview {
  transition: border-color 0.2s ease;
}

.texture-preview:hover {
  border-color: var(--color-border-primary);
}

.control-group {
  transition: opacity 0.2s ease;
}

.color-input {
  transition: all 0.2s ease;
}

/* 加载状态 */
.procedural-texture-generator.loading {
  opacity: 0.7;
  pointer-events: none;
}

.procedural-texture-generator.loading .texture-preview {
  background: linear-gradient(
    90deg,
    var(--color-bg-input) 25%,
    var(--color-bg-secondary) 50%,
    var(--color-bg-input) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 紧凑模式 */
.procedural-texture-generator.compact {
  padding: var(--spacing-sm);
  gap: var(--spacing-sm);
}

.procedural-texture-generator.compact .texture-preview {
  height: 120px;
}

.procedural-texture-generator.compact .control-group {
  gap: var(--spacing-xs);
}

.procedural-texture-generator.compact .control-label {
  font-size: var(--font-size-xs);
}

/* 错误状态 */
.procedural-texture-generator.error .texture-preview {
  border-color: var(--color-error);
  background: var(--color-error-alpha);
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--color-error);
  text-align: center;
  padding: var(--spacing-md);
}

.error-message p {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.error-message small {
  font-size: var(--font-size-xs);
  opacity: 0.8;
  line-height: 1.4;
}
