import * as THREE from 'three';

// 程序化纹理类型
export enum ProceduralTextureType {
  NOISE = 'noise',
  MARBLE = 'marble',
  WOOD = 'wood',
  CARRARA_MARBLE = 'carrara_marble',
  BLACK_MARBLE = 'black_marble',
  GREEN_MARBLE = 'green_marble',
  OAK = 'oak',
  PINE = 'pine',
  WALNUT = 'walnut',
  CHERRY = 'cherry',
  BAMBOO = 'bamboo'
}

// 程序化纹理参数接口
export interface ProceduralTextureParams {
  type: ProceduralTextureType;
  scale: number;
  turbulence?: number;
  ringSpacing?: number;
  grainIntensity?: number;
  knotIntensity?: number;
  veinIntensity?: number;
  color1?: THREE.Color;
  color2?: THREE.Color;
  veinColor?: THREE.Color;
  resolution?: number;
}

// 默认参数
export const DEFAULT_PARAMS: Record<ProceduralTextureType, ProceduralTextureParams> = {
  [ProceduralTextureType.NOISE]: {
    type: ProceduralTextureType.NOISE,
    scale: 5.0,
    turbulence: 1.0,
    resolution: 512
  },
  [ProceduralTextureType.MARBLE]: {
    type: ProceduralTextureType.MARBLE,
    scale: 3.0,
    turbulence: 0.8,
    veinIntensity: 0.5,
    color1: new THREE.Color(0.9, 0.9, 0.9),
    color2: new THREE.Color(0.7, 0.7, 0.75),
    veinColor: new THREE.Color(0.5, 0.5, 0.6),
    resolution: 512
  },
  [ProceduralTextureType.WOOD]: {
    type: ProceduralTextureType.WOOD,
    scale: 2.0,
    ringSpacing: 15.0,
    grainIntensity: 0.3,
    knotIntensity: 0.2,
    color1: new THREE.Color(0.8, 0.6, 0.4),
    color2: new THREE.Color(0.5, 0.3, 0.2),
    resolution: 512
  },
  [ProceduralTextureType.CARRARA_MARBLE]: {
    type: ProceduralTextureType.CARRARA_MARBLE,
    scale: 4.0,
    turbulence: 0.8,
    veinIntensity: 0.3,
    resolution: 512
  },
  [ProceduralTextureType.BLACK_MARBLE]: {
    type: ProceduralTextureType.BLACK_MARBLE,
    scale: 3.5,
    turbulence: 1.2,
    veinIntensity: 0.2,
    resolution: 512
  },
  [ProceduralTextureType.GREEN_MARBLE]: {
    type: ProceduralTextureType.GREEN_MARBLE,
    scale: 3.8,
    turbulence: 1.0,
    veinIntensity: 0.4,
    resolution: 512
  },
  [ProceduralTextureType.OAK]: {
    type: ProceduralTextureType.OAK,
    scale: 2.5,
    ringSpacing: 15.0,
    grainIntensity: 0.3,
    knotIntensity: 0.4,
    resolution: 512
  },
  [ProceduralTextureType.PINE]: {
    type: ProceduralTextureType.PINE,
    scale: 2.2,
    ringSpacing: 20.0,
    grainIntensity: 0.2,
    knotIntensity: 0.2,
    resolution: 512
  },
  [ProceduralTextureType.WALNUT]: {
    type: ProceduralTextureType.WALNUT,
    scale: 2.8,
    ringSpacing: 12.0,
    grainIntensity: 0.4,
    knotIntensity: 0.3,
    resolution: 512
  },
  [ProceduralTextureType.CHERRY]: {
    type: ProceduralTextureType.CHERRY,
    scale: 2.6,
    ringSpacing: 18.0,
    grainIntensity: 0.25,
    knotIntensity: 0.35,
    resolution: 512
  },
  [ProceduralTextureType.BAMBOO]: {
    type: ProceduralTextureType.BAMBOO,
    scale: 3.0,
    grainIntensity: 0.2,
    resolution: 512
  }
};

// 程序化材质类
export class ProceduralMaterial extends THREE.ShaderMaterial {
  private _params: ProceduralTextureParams;
  private _texture: THREE.DataTexture | null = null;

  constructor(params: Partial<ProceduralTextureParams> = {}) {
    const defaultParams = DEFAULT_PARAMS[params.type || ProceduralTextureType.NOISE];
    const finalParams = { ...defaultParams, ...params };

    super({
      uniforms: {
        uTexture: { value: null },
        uScale: { value: finalParams.scale },
        uTurbulence: { value: finalParams.turbulence || 1.0 },
        uRingSpacing: { value: finalParams.ringSpacing || 15.0 },
        uGrainIntensity: { value: finalParams.grainIntensity || 0.3 },
        uKnotIntensity: { value: finalParams.knotIntensity || 0.2 },
        uVeinIntensity: { value: finalParams.veinIntensity || 0.5 },
        uColor1: { value: finalParams.color1 || new THREE.Color(1, 1, 1) },
        uColor2: { value: finalParams.color2 || new THREE.Color(0.5, 0.5, 0.5) },
        uVeinColor: { value: finalParams.veinColor || new THREE.Color(0.3, 0.3, 0.3) },
        uTime: { value: 0 }
      },
      vertexShader: this.getVertexShader(),
      fragmentShader: this.getFragmentShader(finalParams.type)
    });

    this._params = finalParams;
    this.generateTexture();
  }

  private getVertexShader(): string {
    return `
      varying vec2 vUv;
      varying vec3 vPosition;
      varying vec3 vNormal;

      void main() {
        vUv = uv;
        vPosition = position;
        vNormal = normalize(normalMatrix * normal);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;
  }

  private getFragmentShader(type: ProceduralTextureType): string {
    const noiseShader = this.getNoiseShaderCode();
    
    let textureFunction = '';
    
    switch (type) {
      case ProceduralTextureType.MARBLE:
      case ProceduralTextureType.CARRARA_MARBLE:
      case ProceduralTextureType.BLACK_MARBLE:
      case ProceduralTextureType.GREEN_MARBLE:
        textureFunction = this.getMarbleShaderCode();
        break;
      case ProceduralTextureType.WOOD:
      case ProceduralTextureType.OAK:
      case ProceduralTextureType.PINE:
      case ProceduralTextureType.WALNUT:
      case ProceduralTextureType.CHERRY:
      case ProceduralTextureType.BAMBOO:
        textureFunction = this.getWoodShaderCode();
        break;
      default:
        textureFunction = this.getNoiseTextureCode();
    }

    return `
      uniform float uScale;
      uniform float uTurbulence;
      uniform float uRingSpacing;
      uniform float uGrainIntensity;
      uniform float uKnotIntensity;
      uniform float uVeinIntensity;
      uniform vec3 uColor1;
      uniform vec3 uColor2;
      uniform vec3 uVeinColor;
      uniform float uTime;
      
      varying vec2 vUv;
      varying vec3 vPosition;
      varying vec3 vNormal;

      ${noiseShader}
      ${textureFunction}

      void main() {
        vec3 color = generateTexture(vUv);
        gl_FragColor = vec4(color, 1.0);
      }
    `;
  }

  // 获取噪声函数代码
  private getNoiseShaderCode(): string {
    return `
      float random(vec2 st) {
        return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
      }

      float noise(vec2 st) {
        vec2 i = floor(st);
        vec2 f = fract(st);
        float a = random(i);
        float b = random(i + vec2(1.0, 0.0));
        float c = random(i + vec2(0.0, 1.0));
        float d = random(i + vec2(1.0, 1.0));
        vec2 u = f * f * (3.0 - 2.0 * f);
        return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
      }

      float fbm(vec2 st) {
        float value = 0.0;
        float amplitude = 0.5;
        for (int i = 0; i < 6; i++) {
          value += amplitude * noise(st);
          st *= 2.0;
          amplitude *= 0.5;
        }
        return value;
      }
    `;
  }

  // 获取大理石纹理代码
  private getMarbleShaderCode(): string {
    return `
      vec3 generateTexture(vec2 uv) {
        vec2 scaledUV = uv * uScale;
        float basePattern = sin(scaledUV.x * 3.14159 + scaledUV.y * 0.5);
        float noiseValue = fbm(scaledUV * 2.0) * uTurbulence;
        float distortedPattern = basePattern + noiseValue;
        
        float layer1 = sin(distortedPattern * 4.0);
        float layer2 = sin(distortedPattern * 8.0 + noiseValue * 2.0);
        float layer3 = sin(distortedPattern * 16.0 + noiseValue * 4.0);
        
        float combinedPattern = (layer1 + layer2 * 0.5 + layer3 * 0.25) / 1.75;
        float veinPattern = abs(sin(distortedPattern * 12.0 + noiseValue * 6.0));
        veinPattern = smoothstep(0.8, 1.0, veinPattern);
        
        vec3 baseColor = mix(uColor1, uColor2, (combinedPattern + 1.0) * 0.5);
        return mix(baseColor, uVeinColor, veinPattern * uVeinIntensity);
      }
    `;
  }

  // 获取木纹纹理代码
  private getWoodShaderCode(): string {
    return `
      vec3 generateTexture(vec2 uv) {
        vec2 scaledUV = uv * uScale;
        vec2 center = vec2(0.3, 0.7);
        float distanceFromCenter = length(scaledUV - center);
        
        float noise1 = fbm(scaledUV * 6.0) * 0.15;
        float noise2 = fbm(scaledUV * 12.0) * 0.08;
        float distortedDistance = distanceFromCenter + noise1 + noise2;
        
        float rings = sin(distortedDistance * uRingSpacing * 6.28318);
        rings = (rings + 1.0) * 0.5;
        
        float grainDirection = scaledUV.y + fbm(vec2(scaledUV.x * 10.0, scaledUV.y * 2.0)) * 0.2;
        float grain = sin(grainDirection * 50.0) * uGrainIntensity;
        
        vec2 knotCenter1 = vec2(0.2, 0.3);
        vec2 knotCenter2 = vec2(0.8, 0.7);
        float knot1 = 1.0 - smoothstep(0.0, 0.1, length(scaledUV - knotCenter1));
        float knot2 = 1.0 - smoothstep(0.0, 0.08, length(scaledUV - knotCenter2));
        float knots = (knot1 + knot2) * uKnotIntensity;
        
        float woodPattern = rings * 0.6 + grain * 0.3 + knots * 0.1;
        woodPattern = clamp(woodPattern, 0.0, 1.0);
        
        vec3 finalColor = mix(uColor1, uColor2, woodPattern);
        vec3 knotColor = uColor2 * 0.5;
        return mix(finalColor, knotColor, knots * 0.7);
      }
    `;
  }

  // 获取噪声纹理代码
  private getNoiseTextureCode(): string {
    return `
      vec3 generateTexture(vec2 uv) {
        vec2 scaledUV = uv * uScale;
        float noiseValue = fbm(scaledUV) * uTurbulence;
        return mix(uColor1, uColor2, (noiseValue + 1.0) * 0.5);
      }
    `;
  }

  // 生成纹理
  private generateTexture(): void {
    const resolution = this._params.resolution || 512;
    const size = resolution * resolution;
    const data = new Uint8Array(4 * size);

    // 这里可以在 CPU 端生成纹理数据，但为了性能，我们主要依赖 GPU shader
    for (let i = 0; i < size; i++) {
      const stride = i * 4;
      data[stride] = 255;     // R
      data[stride + 1] = 255; // G
      data[stride + 2] = 255; // B
      data[stride + 3] = 255; // A
    }

    this._texture = new THREE.DataTexture(data, resolution, resolution);
    this._texture.needsUpdate = true;
    this.uniforms.uTexture.value = this._texture;
  }

  // 更新参数
  updateParams(newParams: Partial<ProceduralTextureParams>): void {
    this._params = { ...this._params, ...newParams };

    // 批量更新 uniforms 以提高性能
    const uniformsToUpdate: Array<[string, any]> = [];

    if (newParams.scale !== undefined) uniformsToUpdate.push(['uScale', newParams.scale]);
    if (newParams.turbulence !== undefined) uniformsToUpdate.push(['uTurbulence', newParams.turbulence]);
    if (newParams.ringSpacing !== undefined) uniformsToUpdate.push(['uRingSpacing', newParams.ringSpacing]);
    if (newParams.grainIntensity !== undefined) uniformsToUpdate.push(['uGrainIntensity', newParams.grainIntensity]);
    if (newParams.knotIntensity !== undefined) uniformsToUpdate.push(['uKnotIntensity', newParams.knotIntensity]);
    if (newParams.veinIntensity !== undefined) uniformsToUpdate.push(['uVeinIntensity', newParams.veinIntensity]);
    if (newParams.color1) uniformsToUpdate.push(['uColor1', newParams.color1]);
    if (newParams.color2) uniformsToUpdate.push(['uColor2', newParams.color2]);
    if (newParams.veinColor) uniformsToUpdate.push(['uVeinColor', newParams.veinColor]);

    // 批量应用更新
    uniformsToUpdate.forEach(([key, value]) => {
      if (this.uniforms[key]) {
        this.uniforms[key].value = value;
      }
    });

    // 标记材质需要更新
    this.needsUpdate = true;
  }

  // 获取当前参数
  getParams(): ProceduralTextureParams {
    return { ...this._params };
  }

  // 克隆材质
  clone(): ProceduralMaterial {
    const clonedMaterial = new ProceduralMaterial(this._params);
    return clonedMaterial;
  }

  // 验证 Shader 兼容性
  static isShaderSupported(): boolean {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) return false;

      // 检查必要的扩展
      const extensions = [
        'OES_standard_derivatives',
        'EXT_shader_texture_lod'
      ];

      for (const ext of extensions) {
        if (!gl.getExtension(ext)) {
          console.warn(`WebGL extension ${ext} not supported`);
        }
      }

      return true;
    } catch (error) {
      console.error('Shader compatibility check failed:', error);
      return false;
    }
  }

  // 获取性能信息
  getPerformanceInfo(): {
    type: ProceduralTextureType;
    resolution: number;
    complexity: 'low' | 'medium' | 'high'
  } {
    const complexity = this._params.type.includes('marble') ? 'high' :
                      this._params.type.includes('wood') ? 'medium' : 'low';

    return {
      type: this._params.type,
      resolution: this._params.resolution || 512,
      complexity
    };
  }

  // 清理资源
  dispose(): void {
    try {
      super.dispose();
      if (this._texture) {
        this._texture.dispose();
        this._texture = null;
      }
    } catch (error) {
      console.error('Error disposing ProceduralMaterial:', error);
    }
  }
}
