import React, { useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment, OrbitControls } from '@react-three/drei';
import { ProceduralMaterial, ProceduralTextureType, DEFAULT_PARAMS } from './ProceduralMaterial';

// 测试组件 - 用于验证程序化纹理功能
export const ProceduralTextureTest: React.FC = () => {
  const [currentType, setCurrentType] = useState<ProceduralTextureType>(ProceduralTextureType.MARBLE);
  const [material, setMaterial] = useState<ProceduralMaterial | null>(null);

  // 测试所有纹理类型
  const testAllTextures = () => {
    const types = Object.values(ProceduralTextureType);
    let index = 0;

    const switchTexture = () => {
      if (index < types.length) {
        const type = types[index];
        console.log(`测试纹理类型: ${type}`);
        
        try {
          const newMaterial = new ProceduralMaterial({ 
            type, 
            ...DEFAULT_PARAMS[type] 
          });
          
          setCurrentType(type);
          setMaterial(newMaterial);
          
          // 获取性能信息
          const perfInfo = newMaterial.getPerformanceInfo();
          console.log(`${type} 性能信息:`, perfInfo);
          
        } catch (error) {
          console.error(`创建 ${type} 纹理失败:`, error);
        }
        
        index++;
        setTimeout(switchTexture, 2000); // 每2秒切换一次
      } else {
        console.log('所有纹理类型测试完成');
      }
    };

    switchTexture();
  };

  // 性能测试
  const performanceTest = () => {
    console.log('开始性能测试...');
    
    const startTime = performance.now();
    const materials: ProceduralMaterial[] = [];
    
    // 创建多个材质实例
    Object.values(ProceduralTextureType).forEach(type => {
      try {
        const material = new ProceduralMaterial({ 
          type, 
          ...DEFAULT_PARAMS[type] 
        });
        materials.push(material);
      } catch (error) {
        console.error(`创建 ${type} 材质失败:`, error);
      }
    });
    
    const creationTime = performance.now() - startTime;
    console.log(`创建 ${materials.length} 个材质耗时: ${creationTime.toFixed(2)}ms`);
    
    // 测试参数更新性能
    const updateStartTime = performance.now();
    materials.forEach(material => {
      material.updateParams({ scale: Math.random() * 5 + 1 });
    });
    const updateTime = performance.now() - updateStartTime;
    console.log(`更新 ${materials.length} 个材质参数耗时: ${updateTime.toFixed(2)}ms`);
    
    // 清理资源
    materials.forEach(material => material.dispose());
    console.log('性能测试完成');
  };

  // 兼容性测试
  const compatibilityTest = () => {
    console.log('开始兼容性测试...');
    
    const isSupported = ProceduralMaterial.isShaderSupported();
    console.log(`WebGL Shader 支持: ${isSupported ? '是' : '否'}`);
    
    if (!isSupported) {
      console.warn('当前设备可能不支持所有程序化纹理功能');
    }
    
    // 测试各种分辨率
    const resolutions = [256, 512, 1024];
    resolutions.forEach(resolution => {
      try {
        const material = new ProceduralMaterial({
          type: ProceduralTextureType.MARBLE,
          ...DEFAULT_PARAMS[ProceduralTextureType.MARBLE],
          resolution
        });
        console.log(`分辨率 ${resolution}x${resolution}: 支持`);
        material.dispose();
      } catch (error) {
        console.error(`分辨率 ${resolution}x${resolution}: 不支持`, error);
      }
    });
    
    console.log('兼容性测试完成');
  };

  return (
    <div style={{ width: '100%', height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '20px', background: '#f0f0f0' }}>
        <h2>程序化纹理测试</h2>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
          <button onClick={testAllTextures}>测试所有纹理</button>
          <button onClick={performanceTest}>性能测试</button>
          <button onClick={compatibilityTest}>兼容性测试</button>
        </div>
        <p>当前纹理类型: {currentType}</p>
      </div>
      
      <div style={{ flex: 1 }}>
        <Canvas camera={{ position: [0, 0, 5], fov: 45 }}>
          <ambientLight intensity={0.7} />
          <directionalLight position={[5, 5, 5]} intensity={1} />
          
          {/* 测试球体 */}
          <mesh position={[-2, 0, 0]}>
            <sphereGeometry args={[1, 64, 64]} />
            {material && <primitive object={material} attach="material" />}
          </mesh>
          
          {/* 测试立方体 */}
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[1.5, 1.5, 1.5]} />
            {material && <primitive object={material.clone()} attach="material" />}
          </mesh>
          
          {/* 测试平面 */}
          <mesh position={[2, 0, 0]} rotation={[-Math.PI / 2, 0, 0]}>
            <planeGeometry args={[2, 2]} />
            {material && <primitive object={material.clone()} attach="material" />}
          </mesh>
          
          <OrbitControls />
          <Environment preset="city" />
        </Canvas>
      </div>
    </div>
  );
};

export default ProceduralTextureTest;
