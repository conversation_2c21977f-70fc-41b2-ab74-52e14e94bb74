/* 欢迎页面样式 - 根据Figma设计稿精准还原 */
.welcome-page {
  min-height: 100vh;
  /* 根据设计稿的渐变背景 */
  background: radial-gradient(circle at 50% 0%, #000000 46.04%, #0015ff 100%);
  color: var(--color-content-regular);
  position: relative;
}

.welcome-logo {
  position: fixed;
  top: 24px;
  left: 24px;
  width: 100px;
  height: auto;
  opacity: 0.4;
  cursor: pointer;
  transition: opacity 0.2s ease;
  z-index: 100;
}

.welcome-logo:hover {
  opacity: 0.8;
}

/* 主要内容区域 */
.welcome-main {
  padding: 100px 24px 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.welcome-content {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* 欢迎文字区域 */
.welcome-text {
  text-align: center;
  margin-bottom: 48px;
  color: var(--color-content-accent);
}

.welcome-greeting {
  font-size: 32px;
  font-weight: 300;
  margin: 0 0 8px 0;
  opacity: 0.8;
  line-height: 1.2;
}

.welcome-title {
  font-size: 32px;
  font-weight: 500;
  margin: 0 0 80px 0;
  line-height: 1.2;
  background: linear-gradient(90deg, #ffffff 0%, #a0b8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.welcome-subtitle {
  font-size: 18px;
  margin: 0;
  opacity: 0.5;
  line-height: 1.5;
}

/* 模型区域 */
.welcome-models {
  min-height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 模型网格 - 根据Figma设计稿优化布局 */
.models-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  width: 100%;
  max-width: 1000px;
  animation: fadeInUp 0.6s ease-out;
  justify-items: center;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态样式已移至通用Loading组件 */

/* 空状态 */
.welcome-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon {
  font-size: var(--font-size-base);
  margin-bottom: 24px;
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: var(--font-size-lg);
  color: var(--color-content-secondary);
  margin: 0;
  max-width: 400px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .models-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 800px;
    gap: 20px;
  }
}

@media (max-width: 960px) {
  .models-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 720px;
    gap: 18px;
  }
}

@media (max-width: 768px) {
  .models-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    max-width: 480px;
  }

  .welcome-main {
    padding: 80px 16px 32px;
  }
}

@media (max-width: 480px) {
  .models-grid {
    grid-template-columns: 1fr;
    max-width: 240px;
    gap: 16px;
  }
}