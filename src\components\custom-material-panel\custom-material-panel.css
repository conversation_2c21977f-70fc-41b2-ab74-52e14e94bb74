.custom-material {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.material-property-group {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.material-property-group.column {
  flex-direction: column;
  align-items: flex-start;
}

.material-property-group.column .property-label {
  width: 100%;
  margin-bottom: 4px;
}

.property-label {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
  width: 90px;
}

.color-picker-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

/* 自定义react-colorful组件的样式 */
.react-colorful {
  width: 100% !important;
  height: 146px !important;
  border-radius: var(--radius-base);
  overflow: hidden;
  outline: 1px var(--color-border) solid;
  outline-offset: -1px;
  margin-bottom: 8px;
}

/* 双色板样式 */
.react-colorful__saturation {
  border-radius: 0;
  border-bottom: none;
}

/* 色相条样式 */
.react-colorful__hue {
  height: 24px;
  border-radius: 0 0 var(--radius-base) var(--radius-base);
}

/* 调整选择器指示点样式 */
.react-colorful__pointer {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  box-shadow: var(--shadow-base);
  border: 2px solid var(--color-content-invert);
}
/* 吸管图标 */
.color-pipette-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-s);
  background-color: var(--color-bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.color-pipette-icon:hover {
  background-color: var(--color-bg-hover);
}

.texture-upload-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  width: 100%;
  height: 150px;
  background: var(--color-bg-overlay);
  border-radius: var(--radius-lg);
  overflow: hidden;
  cursor: pointer;
}

.plus-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  /* 移除宽高和背景，仅用于居中加号图标 */
}

.upload-text {
  font-size: var(--font-size-base);
  color: var(--color-content-regular, rgba(255, 255, 255, 0.70));
  font-weight: 400;
}

.texture-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 纹理模式切换按钮 */
.property-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.texture-mode-toggle {
  background: var(--color-bg-overlay);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-s);
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-content-regular);
}

.texture-mode-toggle:hover {
  background: var(--color-bg-hover);
  border-color: var(--color-primary);
}

/* 程序化纹理容器 */
.procedural-texture-container {
  width: 100%;
}

.texture-select-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120px;
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--color-bg-overlay);
  gap: 8px;
  color: var(--color-content-regular);
}

.texture-select-button:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-alpha);
}

.texture-select-button span {
  font-size: var(--font-size-base);
  color: var(--color-content-regular);
}

/* 紧凑模式的程序化纹理选择器 */
.procedural-texture-selector.compact {
  max-height: 300px;
  overflow-y: auto;
}