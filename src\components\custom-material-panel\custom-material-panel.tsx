import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Droplet, Palette, Plus, Settings } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import './custom-material-panel.css';
import { Slider } from '../slider/slider';
import { ProceduralTextureSelector, ProceduralMaterial } from '../procedural-texture-generator';

interface CustomMaterialPanelProps {
  onChange?: (material: MaterialSettings) => void;
  defaultSettings?: MaterialSettings;
}

export interface MaterialSettings {
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  textureUrl?: string;
  proceduralMaterial?: ProceduralMaterial;
  useProceduralTexture?: boolean;
}

export const CustomMaterialPanel: React.FC<CustomMaterialPanelProps> = ({ 
  onChange, 
  defaultSettings
}) => {
  // 设置默认值
  const defaultColor = defaultSettings?.color || '#B39B9C';
  const defaultMetalness = defaultSettings?.metalness ?? 0.5;
  const defaultRoughness = defaultSettings?.roughness ?? 0.5;
  const defaultOpacity = defaultSettings?.opacity ?? 1;

  // 状态
  const [color, setColor] = useState<string>(defaultColor);
  const [metalness, setMetalness] = useState<number>(defaultMetalness);
  const [roughness, setRoughness] = useState<number>(defaultRoughness);
  const [opacity, setOpacity] = useState<number>(defaultOpacity);
  const [textureUrl, setTextureUrl] = useState<string | undefined>(defaultSettings?.textureUrl);
  const [proceduralMaterial, setProceduralMaterial] = useState<ProceduralMaterial | undefined>(defaultSettings?.proceduralMaterial);
  const [useProceduralTexture, setUseProceduralTexture] = useState<boolean>(defaultSettings?.useProceduralTexture || false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showTextureSelector, setShowTextureSelector] = useState(false);

  // 更新材质设置
  const updateSettings = (
    newColor?: string,
    newMetalness?: number,
    newRoughness?: number,
    newOpacity?: number,
    newTextureUrl?: string,
    newProceduralMaterial?: ProceduralMaterial,
    newUseProceduralTexture?: boolean
  ) => {
    const updatedSettings: MaterialSettings = {
      color: newColor ?? color,
      metalness: newMetalness ?? metalness,
      roughness: newRoughness ?? roughness,
      opacity: newOpacity ?? opacity,
      textureUrl: newTextureUrl ?? textureUrl,
      proceduralMaterial: newProceduralMaterial ?? proceduralMaterial,
      useProceduralTexture: newUseProceduralTexture ?? useProceduralTexture,
    };

    onChange?.(updatedSettings);
  };

  // 处理颜色变化
  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    updateSettings(newColor);
  };

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const url = URL.createObjectURL(file);
    setTextureUrl(url);
    updateSettings(undefined, undefined, undefined, undefined, url);
  };

  // 使用吸管工具
  const handleEyeDropper = async () => {
    if ('EyeDropper' in window) {
      try {
        // @ts-expect-error - EyeDropper API 可能不在所有TypeScript类型中
        const eyeDropper = new window.EyeDropper();
        const result = await eyeDropper.open();
        setColor(result.sRGBHex);
        updateSettings(result.sRGBHex);
      } catch (error) {
        console.error('EyeDropper error:', error);
      }
    } else {
      console.warn('EyeDropper API not supported');
    }
  };

  // 处理程序化纹理选择
  const handleProceduralTextureSelect = (material: ProceduralMaterial) => {
    setProceduralMaterial(material);
    setUseProceduralTexture(true);
    setTextureUrl(undefined); // 清除传统纹理
    setShowTextureSelector(false);
    updateSettings(undefined, undefined, undefined, undefined, undefined, material, true);
  };

  // 切换纹理模式
  const toggleTextureMode = () => {
    const newUseProceduralTexture = !useProceduralTexture;
    setUseProceduralTexture(newUseProceduralTexture);

    if (newUseProceduralTexture) {
      setTextureUrl(undefined);
      setShowTextureSelector(true);
    } else {
      setProceduralMaterial(undefined);
      setShowTextureSelector(false);
    }

    updateSettings(undefined, undefined, undefined, undefined, undefined,
                  newUseProceduralTexture ? proceduralMaterial : undefined,
                  newUseProceduralTexture);
  };

  return (
    <div className="custom-material" data-layer="自定义材质">
      <div className="material-property-group column" data-layer="Frame 37">
        <div className="property-label" data-layer="颜色">颜色</div>
        <div className="color-picker-container">
          <HexColorPicker color={color} onChange={handleColorChange} />
          <Pipette 
            className="color-pipette-icon" 
            onClick={handleEyeDropper} 
            data-states="default" 
          />
        </div>
      </div>
      
      <div className="material-property-group" data-layer="Frame 36">
        <div className="property-label" data-layer="金属度">金属度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={metalness}
          onChange={(value) => {
            setMetalness(value);
            updateSettings(undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 34">
        <div className="property-label" data-layer="粗糙度">粗糙度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={roughness}
          onChange={(value) => {
            setRoughness(value);
            updateSettings(undefined, undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 35">
        <div className="property-label" data-layer="透明度">透明度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={opacity}
          onChange={(value) => {
            setOpacity(value);
            updateSettings(undefined, undefined, undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group column" data-layer="Frame 43">
        <div className="property-label" data-layer="纹理">
          纹理
          <button
            className="texture-mode-toggle"
            onClick={toggleTextureMode}
            title={useProceduralTexture ? '切换到图片纹理' : '切换到程序化纹理'}
          >
            {useProceduralTexture ? <Palette size={16} /> : <Plus size={16} />}
          </button>
        </div>

        {useProceduralTexture ? (
          <div className="procedural-texture-container">
            {showTextureSelector ? (
              <ProceduralTextureSelector
                onTextureSelect={handleProceduralTextureSelect}
                className="compact"
              />
            ) : (
              <button
                className="texture-select-button"
                onClick={() => setShowTextureSelector(true)}
              >
                <Palette size={20} />
                <span>选择程序化纹理</span>
              </button>
            )}
          </div>
        ) : (
          <label className="texture-upload-area" data-layer="Frame 32">
            {textureUrl ? (
              <img src={textureUrl} alt="纹理贴图" className="texture-preview" />
            ) : (
              <>
                <div className="plus-icon" data-layer="plus">
                  <Plus size={20} color="var(--color-content-regular)" />
                </div>
                <div className="upload-text" data-layer="上传图片">上传图片</div>
              </>
            )}
            <input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              style={{ display: 'none' }}
            />
          </label>
        )}
      </div>
    </div>
  );
};
