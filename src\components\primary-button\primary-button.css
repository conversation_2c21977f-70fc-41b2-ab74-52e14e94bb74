.primary-button {
  display: inline-flex;
  align-items: center;
  height: 32px;
  gap: 4px;
  background: var(--color-brand);
  color: var(--color-content-invert);
  border: none;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  padding: 8px 16px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.primary-button:hover:not(:disabled) {
  background: var(--color-brand-hover);
}

.primary-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.primary-button__icon {
  width: 16px;
  height: 16px;
}

/* Size variants */
.primary-button--small {
  padding: 6px 12px;
  font-size: var(--font-size-base);
}
.primary-button--large {
  padding: 10px 20px;
  font-size: var(--font-size-base);
}

/* Full width */
.primary-button--full-width {
  width: 100%;
  justify-content: center;
}