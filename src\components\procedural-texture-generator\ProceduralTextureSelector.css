.procedural-texture-selector {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--color-bg-secondary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-subtle);
}

.selector-header {
  text-align: center;
  margin-bottom: var(--spacing-sm);
}

.selector-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-content-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.selector-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  margin: 0;
}

.texture-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: var(--spacing-sm);
  max-height: 400px;
  overflow-y: auto;
  padding: var(--spacing-xs);
}

.texture-preview-card {
  display: flex;
  flex-direction: column;
  background: var(--color-bg-primary);
  border: 2px solid var(--color-border-subtle);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.texture-preview-card:hover {
  border-color: var(--color-border-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--color-shadow-light);
}

.texture-preview-card.selected {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.texture-preview-card.selected::after {
  content: "✓";
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  width: 20px;
  height: 20px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  z-index: 1;
}

.texture-preview-canvas {
  width: 100%;
  height: 100px;
  background: var(--color-bg-input);
  position: relative;
}

.texture-info {
  padding: var(--spacing-sm);
  text-align: center;
}

.texture-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.2;
}

.texture-description {
  font-size: var(--font-size-xs);
  color: var(--color-content-secondary);
  margin: 0;
  line-height: 1.3;
}

/* 滚动条样式 */
.texture-grid::-webkit-scrollbar {
  width: 6px;
}

.texture-grid::-webkit-scrollbar-track {
  background: var(--color-bg-tertiary);
  border-radius: 3px;
}

.texture-grid::-webkit-scrollbar-thumb {
  background: var(--color-border-primary);
  border-radius: 3px;
}

.texture-grid::-webkit-scrollbar-thumb:hover {
  background: var(--color-content-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .procedural-texture-selector {
    padding: var(--spacing-sm);
  }
  
  .texture-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-xs);
    max-height: 300px;
  }
  
  .texture-preview-canvas {
    height: 80px;
  }
  
  .texture-info {
    padding: var(--spacing-xs);
  }
  
  .texture-name {
    font-size: var(--font-size-xs);
  }
  
  .texture-description {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .texture-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 深色主题适配 */
[data-theme="dark"] .procedural-texture-selector {
  background: var(--color-bg-secondary);
  border-color: var(--color-border-subtle);
}

[data-theme="dark"] .texture-preview-card {
  background: var(--color-bg-primary);
  border-color: var(--color-border-subtle);
}

[data-theme="dark"] .texture-preview-card:hover {
  border-color: var(--color-border-primary);
  box-shadow: 0 4px 12px var(--color-shadow-dark);
}

[data-theme="dark"] .texture-preview-canvas {
  background: var(--color-bg-input);
}

/* 加载状态 */
.texture-preview-card.loading .texture-preview-canvas {
  background: linear-gradient(
    90deg,
    var(--color-bg-input) 25%,
    var(--color-bg-secondary) 50%,
    var(--color-bg-input) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 空状态 */
.texture-grid:empty::after {
  content: "暂无可用纹理";
  grid-column: 1 / -1;
  text-align: center;
  color: var(--color-content-secondary);
  font-size: var(--font-size-sm);
  padding: var(--spacing-xl);
}

/* 紧凑模式 */
.procedural-texture-selector.compact {
  padding: var(--spacing-sm);
  gap: var(--spacing-sm);
}

.procedural-texture-selector.compact .texture-grid {
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  max-height: 250px;
}

.procedural-texture-selector.compact .texture-preview-canvas {
  height: 70px;
}

.procedural-texture-selector.compact .texture-info {
  padding: var(--spacing-xs);
}

.procedural-texture-selector.compact .texture-name {
  font-size: var(--font-size-xs);
}

.procedural-texture-selector.compact .texture-description {
  display: none;
}
